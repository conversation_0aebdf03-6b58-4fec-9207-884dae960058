
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on 2025/8/3$ at 15:18$
@author: 加油！！！李爸爸
@summary: Brief description of the script's purpose.
"""

''''
1.python基础（元组，列表，字典）
2.python基础进阶（多线程，协成，桌面应用开发 tktinter，exe）
3.前端学习（前端单件套，vue，uniapp）
4.服务器linux（ps -ef｜grep nginx）
5.web框架学习（flask，django）
6.python爬虫（requests，xpath（提取html），bs4）
7.python爬虫逆向（加密算法，aes，des，rsa，hook脚本）
8.数据分析 pyecharts 可视化


全栈学习课程：

通过网盘分享的文件：
链接: https://pan.baidu.com/s/1DrNC72DUM2mqADausd47pw?pwd=qcg6 提取码: qcg6 复制这段内容后打开百度网盘手机App，操作更方便哦 
--来自百度网盘超级会员v5的分享

gui编程：
通过网盘分享的文件：
链接: https://pan.baidu.com/s/1mUvORriZBePrIdVPD8PHqQ?pwd=ffru 提取码: ffru 复制这段内容后打开百度网盘手机App，操作更方便哦 
--来自百度网盘超级会员v5的分享

vue：
通过网盘分享的文件：
链接: https://pan.baidu.com/s/1G5PhKyvA93Od8x7YtpJ5Bw?pwd=2qdk 提取码: 2qdk 复制这段内容后打开百度网盘手机App，操作更方便哦 
--来自百度网盘超级会员v5的分享

'''

def main():
    print("hello python")


if __name__ == "__main__":
    main()
