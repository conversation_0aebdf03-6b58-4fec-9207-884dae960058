from tkinter import *
class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()


    def createWidget(self):
        self.label01 = Label(self, text="用户名")
        self.label01.pack()

        self.v1 = StringVar()
        self.ventry01 = Entry(self, textvariable=self.v1)
        self.ventry01.pack()

        self.btn01 = Button(self, text="登录", command=self.login)
        self.btn01.pack()
    
    def login(self):
       
        if self.v1.get() == "root":
            self.destroy()
            self.open_main()
        else:

            MessageBox.showinfo("提示", "用户名或密码错误")

    def open_main():
        root = Tk()
        root.title("主窗口")
        root.geometry("500x500+500+300")
        Label(root, text="欢迎进入主界面！", font=("Arial", 16)).pack(pady=50)

        root.mainloop()


if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()