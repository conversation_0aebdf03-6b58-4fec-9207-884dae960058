from tkinter import *

class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()
    
    def createWidget(self):
        self.label01 = Label(self, text="用户名")
        self.label01.pack()

        self.v1 = StringVar()
        self.entry01 = Entry(self, textvariable=self.v1)
        self.entry01.pack()


        self.label02 = Label(self, text="密码")
        self.label02.pack()

        self.v2 = StringVar()
        self.entry02 =Entry(self, show="*", textvariable=self.v2)
        self.entry02.pack()

        self.btn01 = Button(self, text="登录", command=self.login)
        self.btn01.pack()
    def login(self):
        print(self.v1.get())
        print(self.v2.get())

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()