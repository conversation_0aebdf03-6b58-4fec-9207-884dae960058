from tkinter import *
from tkinter import messagebox
class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()


    def createWidget(self):
        self.label01 = Label(self, text="用户名")
        self.label01.pack()

        self.v1 = StringVar()
        self.ventry01 = Entry(self, textvariable=self.v1)
        self.ventry01.pack()

        self.btn01 = Button(self, text="登录", command=self.login)
        self.btn01.pack()
    
    def login(self):
        if self.v1.get() == "root":
            messagebox.showinfo("成功", "登录成功！")
            self.open_main()
        else:
            messagebox.showerror("错误", "用户名错误，请输入 'root'")

    def open_main(self):
        # 使用Toplevel创建新窗口，而不是新的Tk实例
        main_window = Toplevel(self.master)
        main_window.title("主窗口")
        main_window.geometry("400x300+600+400")
        Label(main_window, text="欢迎进入主界面！", font=("Arial", 16)).pack(pady=50)

        # 添加关闭按钮
        Button(main_window, text="关闭", command=main_window.destroy).pack()


if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()