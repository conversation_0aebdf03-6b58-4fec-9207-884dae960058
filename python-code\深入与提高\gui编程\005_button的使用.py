from tkinter import *

class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()

    def createWidget(self):
        self.btn01 = <PERSON><PERSON>(self, text="按钮1",command=self.btnClick)
        self.btn01.pack() 

    def btnClick(self):
        print("按钮1被点击了")

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()
