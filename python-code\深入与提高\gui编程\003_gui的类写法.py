from tkinter import *

class Application(Frame):
    def __init__(self,master):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidgets()

    def createWidgets(self):
        self.btn01 = <PERSON><PERSON>(self, text="点我就送花", bg="red", fg="yellow")
        self.btn01.pack()
        self.btn01.bind("<Button-1>", self.songhua)

        self.btnQuit = Button(self, text="退出", command=self.master.destroy)
        self.btnQuit.pack()

    def songhua(self, event):
        print("送你一朵花")

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)


    app.mainloop()

